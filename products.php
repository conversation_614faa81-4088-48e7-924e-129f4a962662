<?php
session_start();
require_once 'config/database.php';

$db = new Database();
$message = '';

// إنشاء مجلد الصور إذا لم يكن موجوداً
if (!file_exists('uploads')) {
    mkdir('uploads', 0777, true);
}

// معالجة إضافة منتج جديد
if ($_POST && isset($_POST['action']) && $_POST['action'] == 'add_product') {
    $name = trim($_POST['name']);
    $description = trim($_POST['description']);
    $price = floatval($_POST['price']);
    $image_path = '';
    
    // معالجة رفع الصورة
    if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $file_type = $_FILES['image']['type'];
        
        if (in_array($file_type, $allowed_types)) {
            $file_extension = pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION);
            $new_filename = uniqid() . '.' . $file_extension;
            $upload_path = 'uploads/' . $new_filename;
            
            if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                $image_path = $upload_path;
            }
        }
    }
    
    if (!empty($name)) {
        $db->query("INSERT INTO products (name, description, image, price) VALUES (:name, :description, :image, :price)");
        $db->bind(':name', $name);
        $db->bind(':description', $description);
        $db->bind(':image', $image_path);
        $db->bind(':price', $price);
        
        if ($db->execute()) {
            $message = '<div class="alert alert-success">تم إضافة المنتج بنجاح!</div>';
        } else {
            $message = '<div class="alert alert-danger">حدث خطأ في إضافة المنتج!</div>';
        }
    } else {
        $message = '<div class="alert alert-warning">يرجى إدخال اسم المنتج!</div>';
    }
}

// معالجة حذف منتج
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $product_id = $_GET['delete'];
    
    // جلب معلومات المنتج لحذف الصورة
    $db->query("SELECT image FROM products WHERE id = :id");
    $db->bind(':id', $product_id);
    $product = $db->single();
    
    if ($product && !empty($product['image']) && file_exists($product['image'])) {
        unlink($product['image']);
    }
    
    $db->query("DELETE FROM products WHERE id = :id");
    $db->bind(':id', $product_id);
    
    if ($db->execute()) {
        $message = '<div class="alert alert-success">تم حذف المنتج بنجاح!</div>';
    } else {
        $message = '<div class="alert alert-danger">حدث خطأ في حذف المنتج!</div>';
    }
}

// جلب جميع المنتجات
$db->query("SELECT * FROM products ORDER BY created_at DESC");
$products = $db->resultset();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - WIDDX OMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
        }
        .product-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 10px;
        }
        .product-card {
            transition: transform 0.3s ease;
        }
        .product-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-box"></i> WIDDX OMS
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="index.php"><i class="fas fa-home"></i> الرئيسية</a>
                <a class="nav-link" href="customers.php"><i class="fas fa-users"></i> العملاء</a>
                <a class="nav-link active" href="products.php"><i class="fas fa-cube"></i> المنتجات</a>
                <a class="nav-link" href="orders.php"><i class="fas fa-shopping-cart"></i> الطلبيات</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <?php echo $message; ?>
        
        <div class="row">
            <!-- نموذج إضافة منتج جديد -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-plus"></i> إضافة منتج جديد</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="action" value="add_product">
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">اسم المنتج *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">وصف المنتج</label>
                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="price" class="form-label">السعر</label>
                                <input type="number" class="form-control" id="price" name="price" step="0.01" min="0">
                            </div>
                            
                            <div class="mb-3">
                                <label for="image" class="form-label">صورة المنتج</label>
                                <input type="file" class="form-control" id="image" name="image" accept="image/*">
                                <small class="form-text text-muted">يُقبل: JPG, PNG, GIF</small>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-plus"></i> إضافة المنتج
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- قائمة المنتجات -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cube"></i> قائمة المنتجات</h5>
                    </div>
                    <div class="card-body">
                        <?php if (count($products) > 0): ?>
                            <div class="row">
                                <?php foreach ($products as $product): ?>
                                    <div class="col-md-6 mb-3">
                                        <div class="card product-card h-100">
                                            <div class="card-body">
                                                <div class="d-flex">
                                                    <div class="me-3">
                                                        <?php if (!empty($product['image']) && file_exists($product['image'])): ?>
                                                            <img src="<?php echo $product['image']; ?>" 
                                                                 alt="<?php echo htmlspecialchars($product['name']); ?>" 
                                                                 class="product-image">
                                                        <?php else: ?>
                                                            <div class="product-image bg-light d-flex align-items-center justify-content-center">
                                                                <i class="fas fa-image text-muted"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <h6 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h6>
                                                        <?php if (!empty($product['description'])): ?>
                                                            <p class="card-text small text-muted">
                                                                <?php echo htmlspecialchars(substr($product['description'], 0, 100)); ?>
                                                                <?php if (strlen($product['description']) > 100) echo '...'; ?>
                                                            </p>
                                                        <?php endif; ?>
                                                        <?php if ($product['price'] > 0): ?>
                                                            <p class="text-success fw-bold"><?php echo number_format($product['price'], 2); ?> جنيه</p>
                                                        <?php endif; ?>
                                                        <div class="btn-group btn-group-sm">
                                                            <a href="?delete=<?php echo $product['id']; ?>" 
                                                               class="btn btn-outline-danger" 
                                                               onclick="return confirm('هل أنت متأكد من حذف هذا المنتج؟')"
                                                               title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-cube fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد منتجات مضافة حتى الآن</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
