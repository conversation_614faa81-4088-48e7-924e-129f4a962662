<?php
// ملف تحديث قاعدة البيانات لإصلاح أي مشاكل
require_once 'config/database.php';

try {
    $pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>تحديث قاعدة البيانات</h2>";
    
    // التحقق من وجود العمود order_date وإزالته إذا كان موجوداً
    try {
        $pdo->exec("ALTER TABLE orders DROP COLUMN order_date");
        echo "✅ تم حذف العمود order_date من جدول orders<br>";
    } catch (Exception $e) {
        echo "ℹ️ العمود order_date غير موجود أو تم حذفه مسبقاً<br>";
    }
    
    // التأكد من وجود العمود updated_at في جدول customers
    try {
        $pdo->exec("ALTER TABLE customers ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
        echo "✅ تم إضافة العمود updated_at إلى جدول customers<br>";
    } catch (Exception $e) {
        echo "ℹ️ العمود updated_at موجود مسبقاً في جدول customers<br>";
    }
    
    // التحقق من بنية الجداول
    echo "<h3>بنية الجداول:</h3>";
    
    // جدول العملاء
    $result = $pdo->query("DESCRIBE customers");
    echo "<strong>جدول customers:</strong><br>";
    while ($row = $result->fetch()) {
        echo "- " . $row['Field'] . " (" . $row['Type'] . ")<br>";
    }
    echo "<br>";
    
    // جدول الطلبيات
    $result = $pdo->query("DESCRIBE orders");
    echo "<strong>جدول orders:</strong><br>";
    while ($row = $result->fetch()) {
        echo "- " . $row['Field'] . " (" . $row['Type'] . ")<br>";
    }
    echo "<br>";
    
    // جدول عناصر الطلبيات
    $result = $pdo->query("DESCRIBE order_items");
    echo "<strong>جدول order_items:</strong><br>";
    while ($row = $result->fetch()) {
        echo "- " . $row['Field'] . " (" . $row['Type'] . ")<br>";
    }
    echo "<br>";
    
    // إحصائيات البيانات
    echo "<h3>إحصائيات البيانات:</h3>";
    
    $result = $pdo->query("SELECT COUNT(*) as count FROM customers");
    $customers_count = $result->fetch()['count'];
    echo "عدد العملاء: " . $customers_count . "<br>";
    
    $result = $pdo->query("SELECT COUNT(*) as count FROM orders");
    $orders_count = $result->fetch()['count'];
    echo "عدد الطلبيات: " . $orders_count . "<br>";
    
    $result = $pdo->query("SELECT COUNT(*) as count FROM order_items");
    $items_count = $result->fetch()['count'];
    echo "عدد عناصر الطلبيات: " . $items_count . "<br>";
    
    echo "<br><strong>✅ تم تحديث قاعدة البيانات بنجاح!</strong><br>";
    echo "<a href='index.php' class='btn btn-primary'>العودة للنظام</a>";
    
} catch(PDOException $e) {
    echo "❌ خطأ في تحديث قاعدة البيانات: " . $e->getMessage();
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    direction: rtl;
}
.btn {
    display: inline-block;
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    margin-top: 20px;
}
.btn:hover {
    background-color: #0056b3;
}
</style>
