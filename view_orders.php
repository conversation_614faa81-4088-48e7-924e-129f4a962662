<?php
session_start();
require_once 'config/database.php';

$db = new Database();
$message = '';

// معالجة تحديث حالة الطلبية
if (isset($_POST['update_status'])) {
    $order_id = intval($_POST['order_id']);
    $status = $_POST['status'];
    
    $db->query("UPDATE orders SET status = :status WHERE id = :id");
    $db->bind(':status', $status);
    $db->bind(':id', $order_id);
    
    if ($db->execute()) {
        $message = '<div class="alert alert-success">تم تحديث حالة الطلبية بنجاح!</div>';
    }
}

// معالجة حذف طلبية
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $order_id = $_GET['delete'];
    
    $db->query("DELETE FROM orders WHERE id = :id");
    $db->bind(':id', $order_id);
    
    if ($db->execute()) {
        $message = '<div class="alert alert-success">تم حذف الطلبية بنجاح!</div>';
    } else {
        $message = '<div class="alert alert-danger">حدث خطأ في حذف الطلبية!</div>';
    }
}

// جلب العملاء للفلترة
$db->query("SELECT id, name FROM customers ORDER BY name");
$customers = $db->resultset();

// جلب الطلبيات مع معلومات العملاء
$customer_filter = isset($_GET['customer_id']) ? intval($_GET['customer_id']) : 0;
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';

$where_conditions = [];
$params = [];

if ($customer_filter > 0) {
    $where_conditions[] = "o.customer_id = :customer_id";
    $params[':customer_id'] = $customer_filter;
}

if (!empty($status_filter)) {
    $where_conditions[] = "o.status = :status";
    $params[':status'] = $status_filter;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

$db->query("SELECT o.*, c.name as customer_name, c.phone as customer_phone 
           FROM orders o 
           JOIN customers c ON o.customer_id = c.id 
           $where_clause 
           ORDER BY o.created_at DESC");

foreach ($params as $param => $value) {
    $db->bind($param, $value);
}

$orders = $db->resultset();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض الطلبيات - WIDDX OMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        .order-item {
            background-color: #ffffff;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border: 1px solid #e9ecef;
            transition: transform 0.2s ease;
        }
        .order-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-box"></i> WIDDX OMS
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="add_customer.php">
                            <i class="fas fa-user-plus"></i> إضافة عميل
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="add_order.php">
                            <i class="fas fa-plus-circle"></i> إضافة طلبية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="view_orders.php">
                            <i class="fas fa-list"></i> عرض الطلبيات
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <?php echo $message; ?>
        
        <!-- فلاتر البحث -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-filter"></i> فلترة الطلبيات</h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="customer_id" class="form-label">العميل</label>
                        <select class="form-select" id="customer_id" name="customer_id">
                            <option value="">جميع العملاء</option>
                            <?php foreach ($customers as $customer): ?>
                                <option value="<?php echo $customer['id']; ?>" 
                                        <?php echo ($customer_filter == $customer['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($customer['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="pending" <?php echo ($status_filter == 'pending') ? 'selected' : ''; ?>>معلقة</option>
                            <option value="processing" <?php echo ($status_filter == 'processing') ? 'selected' : ''; ?>>قيد التنفيذ</option>
                            <option value="completed" <?php echo ($status_filter == 'completed') ? 'selected' : ''; ?>>مكتملة</option>
                            <option value="cancelled" <?php echo ($status_filter == 'cancelled') ? 'selected' : ''; ?>>ملغية</option>
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> بحث
                        </button>
                        <a href="view_orders.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- قائمة الطلبيات -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-list"></i> قائمة الطلبيات</h5>
                <a href="add_order.php" class="btn btn-light btn-sm">
                    <i class="fas fa-plus"></i> إضافة طلبية جديدة
                </a>
            </div>
            <div class="card-body">
                <?php if (count($orders) > 0): ?>
                    <?php foreach ($orders as $order): ?>
                        <div class="order-item">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6 class="mb-2">
                                        <i class="fas fa-receipt text-primary"></i> 
                                        طلبية رقم #<?php echo $order['id']; ?>
                                    </h6>
                                    <p class="mb-1">
                                        <i class="fas fa-user text-muted"></i> 
                                        <strong><?php echo htmlspecialchars($order['customer_name']); ?></strong>
                                        <?php if (!empty($order['customer_phone'])): ?>
                                            - <i class="fas fa-phone text-muted"></i> <?php echo htmlspecialchars($order['customer_phone']); ?>
                                        <?php endif; ?>
                                    </p>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar"></i> 
                                        <?php echo date('Y-m-d H:i', strtotime($order['order_date'])); ?>
                                    </small>
                                    
                                    <?php if (!empty($order['notes'])): ?>
                                        <div class="mt-2">
                                            <small class="text-muted">
                                                <i class="fas fa-sticky-note"></i> 
                                                <?php echo htmlspecialchars($order['notes']); ?>
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="col-md-4 text-end">
                                    <?php
                                    $status_class = '';
                                    $status_text = '';
                                    switch ($order['status']) {
                                        case 'pending':
                                            $status_class = 'bg-warning text-dark';
                                            $status_text = 'معلقة';
                                            break;
                                        case 'processing':
                                            $status_class = 'bg-info text-white';
                                            $status_text = 'قيد التنفيذ';
                                            break;
                                        case 'completed':
                                            $status_class = 'bg-success text-white';
                                            $status_text = 'مكتملة';
                                            break;
                                        case 'cancelled':
                                            $status_class = 'bg-danger text-white';
                                            $status_text = 'ملغية';
                                            break;
                                    }
                                    ?>
                                    <span class="badge <?php echo $status_class; ?> status-badge mb-2">
                                        <?php echo $status_text; ?>
                                    </span>
                                    
                                    <?php if ($order['total_amount'] > 0): ?>
                                        <div class="mb-2">
                                            <strong class="text-success">
                                                <i class="fas fa-money-bill"></i> 
                                                <?php echo number_format($order['total_amount'], 2); ?> جنيه
                                            </strong>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="btn-group-vertical btn-group-sm w-100">
                                        <a href="view_order.php?id=<?php echo $order['id']; ?>" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i> عرض التفاصيل
                                        </a>
                                        
                                        <!-- نموذج تحديث الحالة -->
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                            <select name="status" class="form-select form-select-sm" 
                                                    onchange="this.form.submit()">
                                                <option value="pending" <?php echo ($order['status'] == 'pending') ? 'selected' : ''; ?>>معلقة</option>
                                                <option value="processing" <?php echo ($order['status'] == 'processing') ? 'selected' : ''; ?>>قيد التنفيذ</option>
                                                <option value="completed" <?php echo ($order['status'] == 'completed') ? 'selected' : ''; ?>>مكتملة</option>
                                                <option value="cancelled" <?php echo ($order['status'] == 'cancelled') ? 'selected' : ''; ?>>ملغية</option>
                                            </select>
                                            <input type="hidden" name="update_status" value="1">
                                        </form>
                                        
                                        <a href="?delete=<?php echo $order['id']; ?>" 
                                           class="btn btn-outline-danger btn-sm mt-1" 
                                           onclick="return confirm('هل أنت متأكد من حذف هذه الطلبية؟')">
                                            <i class="fas fa-trash"></i> حذف
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد طلبيات</h5>
                        <p class="text-muted">ابدأ بإضافة طلبية جديدة</p>
                        <a href="add_order.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة طلبية جديدة
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
