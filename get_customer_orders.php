<?php
require_once 'config/database.php';

$db = new Database();

// التحقق من وجود معرف العميل
if (!isset($_GET['customer_id']) || !is_numeric($_GET['customer_id'])) {
    echo '<div class="alert alert-danger">معرف العميل غير صحيح</div>';
    exit;
}

$customer_id = intval($_GET['customer_id']);

// جلب طلبيات العميل
$db->query("SELECT o.*, 
           (SELECT COUNT(*) FROM order_items WHERE order_id = o.id) as items_count
           FROM orders o 
           WHERE o.customer_id = :customer_id 
           ORDER BY o.created_at DESC");
$db->bind(':customer_id', $customer_id);
$orders = $db->resultset();

if (count($orders) > 0): ?>
    <?php foreach ($orders as $order): ?>
        <div class="order-row">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <h6 class="mb-1">
                        <i class="fas fa-receipt text-primary"></i> 
                        طلبية #<?php echo $order['id']; ?>
                    </h6>
                    <small class="text-muted">
                        <i class="fas fa-calendar"></i> 
                        <?php echo date('Y-m-d', strtotime($order['order_date'])); ?>
                    </small>
                </div>
                
                <div class="col-md-2 text-center">
                    <span class="badge bg-secondary">
                        <i class="fas fa-box"></i> <?php echo $order['items_count']; ?> منتج
                    </span>
                </div>
                
                <div class="col-md-2 text-center">
                    <?php
                    $status_class = '';
                    $status_text = '';
                    $status_icon = '';
                    switch ($order['status']) {
                        case 'pending':
                            $status_class = 'bg-warning text-dark';
                            $status_text = 'معلقة';
                            $status_icon = 'fas fa-clock';
                            break;
                        case 'processing':
                            $status_class = 'bg-info text-white';
                            $status_text = 'قيد التنفيذ';
                            $status_icon = 'fas fa-cogs';
                            break;
                        case 'completed':
                            $status_class = 'bg-success text-white';
                            $status_text = 'مكتملة';
                            $status_icon = 'fas fa-check-circle';
                            break;
                        case 'cancelled':
                            $status_class = 'bg-danger text-white';
                            $status_text = 'ملغية';
                            $status_icon = 'fas fa-times-circle';
                            break;
                    }
                    ?>
                    <span class="badge <?php echo $status_class; ?>">
                        <i class="<?php echo $status_icon; ?>"></i> <?php echo $status_text; ?>
                    </span>
                </div>
                
                <div class="col-md-2 text-center">
                    <?php if ($order['total_amount'] > 0): ?>
                        <strong class="text-success">
                            <i class="fas fa-money-bill"></i> 
                            <?php echo number_format($order['total_amount'], 0); ?> جنيه
                        </strong>
                    <?php else: ?>
                        <span class="text-muted">غير محدد</span>
                    <?php endif; ?>
                </div>
                
                <div class="col-md-3">
                    <div class="btn-group btn-group-sm w-100">
                        <a href="view_order.php?id=<?php echo $order['id']; ?>" 
                           class="btn btn-outline-primary" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </a>
                        
                        <select class="form-select form-select-sm" 
                                onchange="updateOrderStatus(<?php echo $order['id']; ?>, this.value)"
                                title="تغيير الحالة">
                            <option value="pending" <?php echo ($order['status'] == 'pending') ? 'selected' : ''; ?>>معلقة</option>
                            <option value="processing" <?php echo ($order['status'] == 'processing') ? 'selected' : ''; ?>>قيد التنفيذ</option>
                            <option value="completed" <?php echo ($order['status'] == 'completed') ? 'selected' : ''; ?>>مكتملة</option>
                            <option value="cancelled" <?php echo ($order['status'] == 'cancelled') ? 'selected' : ''; ?>>ملغية</option>
                        </select>
                    </div>
                    
                    <?php if (!empty($order['notes'])): ?>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-sticky-note"></i> 
                                <?php echo htmlspecialchars(substr($order['notes'], 0, 50)); ?>
                                <?php if (strlen($order['notes']) > 50) echo '...'; ?>
                            </small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
    
    <!-- إحصائيات سريعة للعميل -->
    <div class="row mt-3">
        <div class="col-12">
            <div class="alert alert-info">
                <div class="row text-center">
                    <div class="col-3">
                        <strong><?php echo count($orders); ?></strong><br>
                        <small>إجمالي الطلبيات</small>
                    </div>
                    <div class="col-3">
                        <strong><?php echo count(array_filter($orders, function($o) { return $o['status'] == 'pending'; })); ?></strong><br>
                        <small>معلقة</small>
                    </div>
                    <div class="col-3">
                        <strong><?php echo count(array_filter($orders, function($o) { return $o['status'] == 'processing'; })); ?></strong><br>
                        <small>قيد التنفيذ</small>
                    </div>
                    <div class="col-3">
                        <strong><?php echo count(array_filter($orders, function($o) { return $o['status'] == 'completed'; })); ?></strong><br>
                        <small>مكتملة</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php else: ?>
    <div class="text-center py-4">
        <i class="fas fa-shopping-cart fa-2x text-muted mb-3"></i>
        <p class="text-muted">لا توجد طلبيات لهذا العميل</p>
        <a href="add_order.php?customer_id=<?php echo $customer_id; ?>" class="btn btn-primary btn-sm">
            <i class="fas fa-plus"></i> إضافة طلبية جديدة
        </a>
    </div>
<?php endif; ?>
