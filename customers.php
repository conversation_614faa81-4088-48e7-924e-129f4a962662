<?php
session_start();
require_once 'config/database.php';

$db = new Database();
$message = '';

// معالجة إضافة عميل جديد
if ($_POST && isset($_POST['action']) && $_POST['action'] == 'add_customer') {
    $name = trim($_POST['name']);
    $phone = trim($_POST['phone']);
    $email = trim($_POST['email']);
    $address = trim($_POST['address']);
    
    if (!empty($name)) {
        $db->query("INSERT INTO customers (name, phone, email, address) VALUES (:name, :phone, :email, :address)");
        $db->bind(':name', $name);
        $db->bind(':phone', $phone);
        $db->bind(':email', $email);
        $db->bind(':address', $address);
        
        if ($db->execute()) {
            $message = '<div class="alert alert-success">تم إضافة العميل بنجاح!</div>';
        } else {
            $message = '<div class="alert alert-danger">حدث خطأ في إضافة العميل!</div>';
        }
    } else {
        $message = '<div class="alert alert-warning">يرجى إدخال اسم العميل!</div>';
    }
}

// معالجة حذف عميل
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $customer_id = $_GET['delete'];
    
    $db->query("DELETE FROM customers WHERE id = :id");
    $db->bind(':id', $customer_id);
    
    if ($db->execute()) {
        $message = '<div class="alert alert-success">تم حذف العميل بنجاح!</div>';
    } else {
        $message = '<div class="alert alert-danger">حدث خطأ في حذف العميل!</div>';
    }
}

// جلب جميع العملاء
$db->query("SELECT * FROM customers ORDER BY created_at DESC");
$customers = $db->resultset();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - WIDDX OMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-box"></i> WIDDX OMS
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="index.php"><i class="fas fa-home"></i> الرئيسية</a>
                <a class="nav-link active" href="customers.php"><i class="fas fa-users"></i> العملاء</a>
                <a class="nav-link" href="orders.php"><i class="fas fa-shopping-cart"></i> الطلبيات</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <?php echo $message; ?>
        
        <div class="row">
            <!-- نموذج إضافة عميل جديد -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-user-plus"></i> إضافة عميل جديد</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="add_customer">
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">اسم العميل *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-plus"></i> إضافة العميل
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- قائمة العملاء -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-users"></i> قائمة العملاء</h5>
                    </div>
                    <div class="card-body">
                        <?php if (count($customers) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الاسم</th>
                                            <th>الهاتف</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>تاريخ الإضافة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($customers as $customer): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($customer['name']); ?></strong>
                                                    <?php if (!empty($customer['address'])): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($customer['address']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($customer['phone']); ?></td>
                                                <td><?php echo htmlspecialchars($customer['email']); ?></td>
                                                <td><?php echo date('Y-m-d', strtotime($customer['created_at'])); ?></td>
                                                <td>
                                                    <a href="orders.php?customer_id=<?php echo $customer['id']; ?>" 
                                                       class="btn btn-sm btn-success" title="إضافة طلبية">
                                                        <i class="fas fa-plus"></i>
                                                    </a>
                                                    <a href="view_customer.php?id=<?php echo $customer['id']; ?>" 
                                                       class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="?delete=<?php echo $customer['id']; ?>" 
                                                       class="btn btn-sm btn-danger" 
                                                       onclick="return confirm('هل أنت متأكد من حذف هذا العميل؟')"
                                                       title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد عملاء مسجلين حتى الآن</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
