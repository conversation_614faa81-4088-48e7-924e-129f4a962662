<?php
session_start();
require_once 'config/database.php';

$db = new Database();
$message = '';

// معالجة إضافة عميل جديد
if ($_POST && isset($_POST['action']) && $_POST['action'] == 'add_customer') {
    $name = trim($_POST['name']);
    $phone = trim($_POST['phone']);
    $email = trim($_POST['email']);
    $address = trim($_POST['address']);
    
    if (!empty($name)) {
        $db->query("INSERT INTO customers (name, phone, email, address) VALUES (:name, :phone, :email, :address)");
        $db->bind(':name', $name);
        $db->bind(':phone', $phone);
        $db->bind(':email', $email);
        $db->bind(':address', $address);
        
        if ($db->execute()) {
            $customer_id = $db->lastInsertId();
            $message = '<div class="alert alert-success">
                <i class="fas fa-check-circle"></i> تم إضافة العميل بنجاح!
                <div class="mt-2">
                    <a href="add_order.php?customer_id=' . $customer_id . '" class="btn btn-sm btn-success">
                        <i class="fas fa-plus"></i> إضافة طلبية لهذا العميل
                    </a>
                    <a href="add_customer.php" class="btn btn-sm btn-primary">
                        <i class="fas fa-user-plus"></i> إضافة عميل آخر
                    </a>
                </div>
            </div>';
        } else {
            $message = '<div class="alert alert-danger">حدث خطأ في إضافة العميل!</div>';
        }
    } else {
        $message = '<div class="alert alert-warning">يرجى إدخال اسم العميل!</div>';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة عميل جديد - WIDDX OMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-box"></i> WIDDX OMS
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="add_customer.php">
                            <i class="fas fa-user-plus"></i> إضافة عميل
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="add_order.php">
                            <i class="fas fa-plus-circle"></i> إضافة طلبية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="view_orders.php">
                            <i class="fas fa-list"></i> عرض الطلبيات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_customers.php">
                            <i class="fas fa-users-cog"></i> إدارة العملاء
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <?php echo $message; ?>
        
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header text-center">
                        <h4><i class="fas fa-user-plus"></i> إضافة عميل جديد</h4>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="add_customer">
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-user"></i> اسم العميل *
                                </label>
                                <input type="text" class="form-control" id="name" name="name" required 
                                       placeholder="أدخل اسم العميل الكامل">
                            </div>
                            
                            <div class="mb-3">
                                <label for="phone" class="form-label">
                                    <i class="fas fa-phone"></i> رقم الهاتف
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       placeholder="مثال: 01234567890">
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope"></i> البريد الإلكتروني
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       placeholder="مثال: <EMAIL>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">
                                    <i class="fas fa-map-marker-alt"></i> العنوان
                                </label>
                                <textarea class="form-control" id="address" name="address" rows="3" 
                                          placeholder="أدخل العنوان التفصيلي"></textarea>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save"></i> حفظ العميل
                                </button>
                                <a href="index.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-right"></i> العودة للرئيسية
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- نصائح مفيدة -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h6 class="card-title"><i class="fas fa-lightbulb text-warning"></i> نصائح مفيدة</h6>
                        <ul class="mb-0">
                            <li>اسم العميل مطلوب، باقي البيانات اختيارية</li>
                            <li>يمكنك إضافة طلبية مباشرة بعد حفظ العميل</li>
                            <li>تأكد من صحة رقم الهاتف للتواصل</li>
                            <li>العنوان مهم لتسليم الطلبيات</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تركيز على حقل الاسم عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('name').focus();
        });
    </script>
</body>
</html>
