<?php
session_start();
require_once 'config/database.php';

$db = new Database();
$message = '';

// معالجة إضافة طلبية جديدة
if ($_POST && isset($_POST['action']) && $_POST['action'] == 'add_order') {
    $customer_id = intval($_POST['customer_id']);
    $notes = trim($_POST['notes']);
    
    if ($customer_id > 0) {
        try {
            $db->beginTransaction();
            
            // إنشاء الطلبية
            $db->query("INSERT INTO orders (customer_id, notes) VALUES (:customer_id, :notes)");
            $db->bind(':customer_id', $customer_id);
            $db->bind(':notes', $notes);
            $db->execute();
            
            $order_id = $db->lastInsertId();
            
            // إضافة عناصر الطلبية
            if (isset($_POST['products']) && is_array($_POST['products'])) {
                foreach ($_POST['products'] as $index => $product_id) {
                    if (!empty($product_id) && !empty($_POST['quantities'][$index])) {
                        // جلب معلومات المنتج
                        $db->query("SELECT name, image, price FROM products WHERE id = :id");
                        $db->bind(':id', $product_id);
                        $product = $db->single();
                        
                        if ($product) {
                            $quantity = intval($_POST['quantities'][$index]);
                            $width = !empty($_POST['widths'][$index]) ? floatval($_POST['widths'][$index]) : null;
                            $height = !empty($_POST['heights'][$index]) ? floatval($_POST['heights'][$index]) : null;
                            $depth = !empty($_POST['depths'][$index]) ? floatval($_POST['depths'][$index]) : null;
                            $item_notes = trim($_POST['item_notes'][$index]);
                            $unit_price = floatval($product['price']);
                            $total_price = $unit_price * $quantity;
                            
                            $db->query("INSERT INTO order_items (order_id, product_id, product_name, product_image, quantity, width, height, depth, unit_price, total_price, notes) 
                                       VALUES (:order_id, :product_id, :product_name, :product_image, :quantity, :width, :height, :depth, :unit_price, :total_price, :notes)");
                            $db->bind(':order_id', $order_id);
                            $db->bind(':product_id', $product_id);
                            $db->bind(':product_name', $product['name']);
                            $db->bind(':product_image', $product['image']);
                            $db->bind(':quantity', $quantity);
                            $db->bind(':width', $width);
                            $db->bind(':height', $height);
                            $db->bind(':depth', $depth);
                            $db->bind(':unit_price', $unit_price);
                            $db->bind(':total_price', $total_price);
                            $db->bind(':notes', $item_notes);
                            $db->execute();
                        }
                    }
                }
            }
            
            // تحديث إجمالي الطلبية
            $db->query("UPDATE orders SET total_amount = (SELECT SUM(total_price) FROM order_items WHERE order_id = :order_id) WHERE id = :order_id");
            $db->bind(':order_id', $order_id);
            $db->execute();
            
            $db->commit();
            $message = '<div class="alert alert-success">تم إنشاء الطلبية بنجاح! رقم الطلبية: ' . $order_id . '</div>';
            
        } catch (Exception $e) {
            $db->rollBack();
            $message = '<div class="alert alert-danger">حدث خطأ في إنشاء الطلبية: ' . $e->getMessage() . '</div>';
        }
    } else {
        $message = '<div class="alert alert-warning">يرجى اختيار العميل!</div>';
    }
}

// معالجة تحديث حالة الطلبية
if (isset($_POST['update_status'])) {
    $order_id = intval($_POST['order_id']);
    $status = $_POST['status'];
    
    $db->query("UPDATE orders SET status = :status WHERE id = :id");
    $db->bind(':status', $status);
    $db->bind(':id', $order_id);
    
    if ($db->execute()) {
        $message = '<div class="alert alert-success">تم تحديث حالة الطلبية بنجاح!</div>';
    }
}

// جلب العملاء للقائمة المنسدلة
$db->query("SELECT id, name FROM customers ORDER BY name");
$customers = $db->resultset();

// جلب المنتجات للقائمة المنسدلة
$db->query("SELECT id, name, price FROM products ORDER BY name");
$products = $db->resultset();

// جلب الطلبيات مع معلومات العملاء
$customer_filter = isset($_GET['customer_id']) ? intval($_GET['customer_id']) : 0;
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';

$where_conditions = [];
$params = [];

if ($customer_filter > 0) {
    $where_conditions[] = "o.customer_id = :customer_id";
    $params[':customer_id'] = $customer_filter;
}

if (!empty($status_filter)) {
    $where_conditions[] = "o.status = :status";
    $params[':status'] = $status_filter;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

$db->query("SELECT o.*, c.name as customer_name, c.phone as customer_phone 
           FROM orders o 
           JOIN customers c ON o.customer_id = c.id 
           $where_clause 
           ORDER BY o.created_at DESC");

foreach ($params as $param => $value) {
    $db->bind($param, $value);
}

$orders = $db->resultset();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الطلبيات - WIDDX OMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }
        .order-item {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #e9ecef;
        }
        .product-row {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-box"></i> WIDDX OMS
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="index.php"><i class="fas fa-home"></i> الرئيسية</a>
                <a class="nav-link" href="customers.php"><i class="fas fa-users"></i> العملاء</a>
                <a class="nav-link" href="products.php"><i class="fas fa-cube"></i> المنتجات</a>
                <a class="nav-link active" href="orders.php"><i class="fas fa-shopping-cart"></i> الطلبيات</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <?php echo $message; ?>

        <!-- فلاتر البحث -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-filter"></i> فلترة الطلبيات</h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="customer_id" class="form-label">العميل</label>
                        <select class="form-select" id="customer_id" name="customer_id">
                            <option value="">جميع العملاء</option>
                            <?php foreach ($customers as $customer): ?>
                                <option value="<?php echo $customer['id']; ?>"
                                        <?php echo ($customer_filter == $customer['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($customer['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="pending" <?php echo ($status_filter == 'pending') ? 'selected' : ''; ?>>معلقة</option>
                            <option value="processing" <?php echo ($status_filter == 'processing') ? 'selected' : ''; ?>>قيد التنفيذ</option>
                            <option value="completed" <?php echo ($status_filter == 'completed') ? 'selected' : ''; ?>>مكتملة</option>
                            <option value="cancelled" <?php echo ($status_filter == 'cancelled') ? 'selected' : ''; ?>>ملغية</option>
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> بحث
                        </button>
                        <a href="orders.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <div class="row">
            <!-- نموذج إضافة طلبية جديدة -->
            <div class="col-md-5">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-plus"></i> إضافة طلبية جديدة</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="orderForm">
                            <input type="hidden" name="action" value="add_order">

                            <div class="mb-3">
                                <label for="customer_id_form" class="form-label">العميل *</label>
                                <select class="form-select" id="customer_id_form" name="customer_id" required>
                                    <option value="">اختر العميل</option>
                                    <?php foreach ($customers as $customer): ?>
                                        <option value="<?php echo $customer['id']; ?>"
                                                <?php echo ($customer_filter == $customer['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($customer['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">المنتجات</label>
                                <div id="products-container">
                                    <div class="product-row">
                                        <div class="row g-2">
                                            <div class="col-12">
                                                <select class="form-select" name="products[]">
                                                    <option value="">اختر المنتج</option>
                                                    <?php foreach ($products as $product): ?>
                                                        <option value="<?php echo $product['id']; ?>" data-price="<?php echo $product['price']; ?>">
                                                            <?php echo htmlspecialchars($product['name']); ?>
                                                            (<?php echo number_format($product['price'], 2); ?> جنيه)
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                            <div class="col-3">
                                                <input type="number" class="form-control" name="quantities[]" placeholder="الكمية" min="1">
                                            </div>
                                            <div class="col-3">
                                                <input type="number" class="form-control" name="widths[]" placeholder="العرض" step="0.01">
                                            </div>
                                            <div class="col-3">
                                                <input type="number" class="form-control" name="heights[]" placeholder="الارتفاع" step="0.01">
                                            </div>
                                            <div class="col-3">
                                                <input type="number" class="form-control" name="depths[]" placeholder="العمق" step="0.01">
                                            </div>
                                            <div class="col-12">
                                                <input type="text" class="form-control" name="item_notes[]" placeholder="ملاحظات المنتج">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="addProductRow()">
                                    <i class="fas fa-plus"></i> إضافة منتج آخر
                                </button>
                            </div>

                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات الطلبية</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                            </div>

                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-plus"></i> إنشاء الطلبية
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- قائمة الطلبيات -->
            <div class="col-md-7">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-shopping-cart"></i> قائمة الطلبيات</h5>
                    </div>
                    <div class="card-body">
                        <?php if (count($orders) > 0): ?>
                            <?php foreach ($orders as $order): ?>
                                <div class="order-item">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <h6 class="mb-1">
                                                <i class="fas fa-receipt"></i> طلبية رقم #<?php echo $order['id']; ?>
                                            </h6>
                                            <p class="mb-1">
                                                <i class="fas fa-user"></i>
                                                <strong><?php echo htmlspecialchars($order['customer_name']); ?></strong>
                                                <?php if (!empty($order['customer_phone'])): ?>
                                                    - <?php echo htmlspecialchars($order['customer_phone']); ?>
                                                <?php endif; ?>
                                            </p>
                                            <small class="text-muted">
                                                <i class="fas fa-calendar"></i>
                                                <?php echo date('Y-m-d H:i', strtotime($order['order_date'])); ?>
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <?php
                                            $status_class = '';
                                            $status_text = '';
                                            switch ($order['status']) {
                                                case 'pending':
                                                    $status_class = 'bg-warning text-dark';
                                                    $status_text = 'معلقة';
                                                    break;
                                                case 'processing':
                                                    $status_class = 'bg-info text-white';
                                                    $status_text = 'قيد التنفيذ';
                                                    break;
                                                case 'completed':
                                                    $status_class = 'bg-success text-white';
                                                    $status_text = 'مكتملة';
                                                    break;
                                                case 'cancelled':
                                                    $status_class = 'bg-danger text-white';
                                                    $status_text = 'ملغية';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge <?php echo $status_class; ?> status-badge">
                                                <?php echo $status_text; ?>
                                            </span>
                                            <?php if ($order['total_amount'] > 0): ?>
                                                <div class="mt-1">
                                                    <strong class="text-success">
                                                        <?php echo number_format($order['total_amount'], 2); ?> جنيه
                                                    </strong>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <?php if (!empty($order['notes'])): ?>
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-sticky-note"></i>
                                                <?php echo htmlspecialchars($order['notes']); ?>
                                            </small>
                                        </div>
                                    <?php endif; ?>

                                    <div class="d-flex gap-2">
                                        <a href="view_order.php?id=<?php echo $order['id']; ?>"
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> عرض التفاصيل
                                        </a>

                                        <!-- نموذج تحديث الحالة -->
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                            <select name="status" class="form-select form-select-sm d-inline-block w-auto"
                                                    onchange="this.form.submit()">
                                                <option value="pending" <?php echo ($order['status'] == 'pending') ? 'selected' : ''; ?>>معلقة</option>
                                                <option value="processing" <?php echo ($order['status'] == 'processing') ? 'selected' : ''; ?>>قيد التنفيذ</option>
                                                <option value="completed" <?php echo ($order['status'] == 'completed') ? 'selected' : ''; ?>>مكتملة</option>
                                                <option value="cancelled" <?php echo ($order['status'] == 'cancelled') ? 'selected' : ''; ?>>ملغية</option>
                                            </select>
                                            <input type="hidden" name="update_status" value="1">
                                        </form>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد طلبيات</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function addProductRow() {
            const container = document.getElementById('products-container');
            const productRow = document.createElement('div');
            productRow.className = 'product-row';
            productRow.innerHTML = `
                <div class="row g-2">
                    <div class="col-12">
                        <select class="form-select" name="products[]">
                            <option value="">اختر المنتج</option>
                            <?php foreach ($products as $product): ?>
                                <option value="<?php echo $product['id']; ?>" data-price="<?php echo $product['price']; ?>">
                                    <?php echo htmlspecialchars($product['name']); ?>
                                    (<?php echo number_format($product['price'], 2); ?> جنيه)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-3">
                        <input type="number" class="form-control" name="quantities[]" placeholder="الكمية" min="1">
                    </div>
                    <div class="col-3">
                        <input type="number" class="form-control" name="widths[]" placeholder="العرض" step="0.01">
                    </div>
                    <div class="col-3">
                        <input type="number" class="form-control" name="heights[]" placeholder="الارتفاع" step="0.01">
                    </div>
                    <div class="col-3">
                        <input type="number" class="form-control" name="depths[]" placeholder="العمق" step="0.01">
                    </div>
                    <div class="col-10">
                        <input type="text" class="form-control" name="item_notes[]" placeholder="ملاحظات المنتج">
                    </div>
                    <div class="col-2">
                        <button type="button" class="btn btn-sm btn-outline-danger w-100" onclick="removeProductRow(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(productRow);
        }

        function removeProductRow(button) {
            button.closest('.product-row').remove();
        }
    </script>
</body>
</html>
