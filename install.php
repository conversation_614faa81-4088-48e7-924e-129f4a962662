<?php
// ملف إنشاء قاعدة البيانات والجداول
require_once 'config/database.php';

try {
    // إنشاء قاعدة البيانات
    $pdo = new PDO('mysql:host=' . DB_HOST . ';charset=utf8mb4', DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE " . DB_NAME);
    
    // جدول العملاء
    $customers_table = "
    CREATE TABLE IF NOT EXISTS customers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VA<PERSON>HA<PERSON>(255) NOT NULL,
        phone VARCHAR(20),
        email VARCHAR(255),
        address TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    // جدول المنتجات
    $products_table = "
    CREATE TABLE IF NOT EXISTS products (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        image VARCHAR(255),
        price DECIMAL(10,2) DEFAULT 0.00,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    // جدول الطلبيات
    $orders_table = "
    CREATE TABLE IF NOT EXISTS orders (
        id INT AUTO_INCREMENT PRIMARY KEY,
        customer_id INT NOT NULL,
        order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('pending', 'processing', 'completed', 'cancelled') DEFAULT 'pending',
        total_amount DECIMAL(10,2) DEFAULT 0.00,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    // جدول تفاصيل الطلبيات
    $order_items_table = "
    CREATE TABLE IF NOT EXISTS order_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        order_id INT NOT NULL,
        product_id INT NOT NULL,
        product_name VARCHAR(255) NOT NULL,
        product_image VARCHAR(255),
        quantity INT NOT NULL DEFAULT 1,
        width DECIMAL(8,2),
        height DECIMAL(8,2),
        depth DECIMAL(8,2),
        unit_price DECIMAL(10,2) DEFAULT 0.00,
        total_price DECIMAL(10,2) DEFAULT 0.00,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    // تنفيذ إنشاء الجداول
    $pdo->exec($customers_table);
    echo "تم إنشاء جدول العملاء بنجاح<br>";
    
    $pdo->exec($products_table);
    echo "تم إنشاء جدول المنتجات بنجاح<br>";
    
    $pdo->exec($orders_table);
    echo "تم إنشاء جدول الطلبيات بنجاح<br>";
    
    $pdo->exec($order_items_table);
    echo "تم إنشاء جدول تفاصيل الطلبيات بنجاح<br>";
    
    // إدراج بيانات تجريبية
    $sample_customers = "
    INSERT IGNORE INTO customers (id, name, phone, email, address) VALUES
    (1, 'أحمد محمد', '01234567890', '<EMAIL>', 'القاهرة، مصر'),
    (2, 'فاطمة علي', '01987654321', '<EMAIL>', 'الإسكندرية، مصر'),
    (3, 'محمد حسن', '01122334455', '<EMAIL>', 'الجيزة، مصر')";
    
    $sample_products = "
    INSERT IGNORE INTO products (id, name, description, price) VALUES
    (1, 'خزانة ملابس', 'خزانة ملابس خشبية عالية الجودة', 2500.00),
    (2, 'طاولة طعام', 'طاولة طعام خشبية لـ 6 أشخاص', 1800.00),
    (3, 'كرسي مكتب', 'كرسي مكتب مريح وقابل للتعديل', 750.00),
    (4, 'سرير مزدوج', 'سرير مزدوج مع مرتبة', 3200.00),
    (5, 'مكتبة كتب', 'مكتبة كتب خشبية بـ 5 أرفف', 1200.00)";
    
    $pdo->exec($sample_customers);
    echo "تم إدراج العملاء التجريبيين بنجاح<br>";
    
    $pdo->exec($sample_products);
    echo "تم إدراج المنتجات التجريبية بنجاح<br>";
    
    echo "<br><strong>تم إنشاء قاعدة البيانات بنجاح!</strong><br>";
    echo "<a href='index.php'>الانتقال إلى النظام</a>";
    
} catch(PDOException $e) {
    echo "خطأ في إنشاء قاعدة البيانات: " . $e->getMessage();
}
?>
