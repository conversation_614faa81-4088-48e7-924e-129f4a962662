<?php
session_start();
require_once 'config/database.php';

// إنشاء اتصال قاعدة البيانات
$db = new Database();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الطلبيات - WIDDX OMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
            color: #2c3e50 !important;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #667eea;
        }
        .customer-button {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 10px !important;
            transition: all 0.3s ease;
        }
        .customer-button:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .customer-button:not(.collapsed) {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .customer-stats .badge {
            font-size: 0.75rem;
        }
        .accordion-item {
            border: none;
            border-radius: 10px;
            overflow: hidden;
        }
        .accordion-body {
            background-color: #f8f9fa;
        }
        .order-row {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #e9ecef;
            transition: transform 0.2s ease;
        }
        .order-row:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .stats-card.bg-warning {
            background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%) !important;
            color: #212529;
        }
        .stats-card.bg-info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
            color: white;
        }
        .stats-card.bg-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
            color: white;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-box"></i> WIDDX OMS
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="add_customer.php">
                            <i class="fas fa-user-plus"></i> إضافة عميل
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="add_order.php">
                            <i class="fas fa-plus-circle"></i> إضافة طلبية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="view_orders.php">
                            <i class="fas fa-list"></i> عرض الطلبيات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_customers.php">
                            <i class="fas fa-users-cog"></i> إدارة العملاء
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card mb-4">
                    <div class="card-header text-center">
                        <h2><i class="fas fa-home"></i> مرحباً بك في نظام إدارة الطلبيات</h2>
                    </div>
                    <div class="card-body text-center">
                        <p class="lead">نظام شامل لإدارة العملاء والمنتجات والطلبيات</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <?php
            // إحصائيات العملاء
            $db->query("SELECT COUNT(*) as count FROM customers");
            $customers_count = $db->single()['count'];

            // إحصائيات الطلبيات
            $db->query("SELECT COUNT(*) as count FROM orders");
            $orders_count = $db->single()['count'];

            // إحصائيات الطلبيات المعلقة
            $db->query("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'");
            $pending_orders = $db->single()['count'];

            // إحصائيات الطلبيات قيد التنفيذ
            $db->query("SELECT COUNT(*) as count FROM orders WHERE status = 'processing'");
            $processing_orders = $db->single()['count'];

            // إحصائيات الطلبيات المكتملة
            $db->query("SELECT COUNT(*) as count FROM orders WHERE status = 'completed'");
            $completed_orders = $db->single()['count'];
            ?>

            <div class="col-md-2 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h3><?php echo $customers_count; ?></h3>
                        <p>العملاء</p>
                    </div>
                </div>
            </div>

            <div class="col-md-2 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                        <h3><?php echo $orders_count; ?></h3>
                        <p>إجمالي الطلبيات</p>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-warning">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h3><?php echo $pending_orders; ?></h3>
                        <p>طلبيات معلقة</p>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-info">
                    <div class="card-body text-center">
                        <i class="fas fa-cogs fa-2x mb-2"></i>
                        <h3><?php echo $processing_orders; ?></h3>
                        <p>قيد التنفيذ</p>
                    </div>
                </div>
            </div>

            <div class="col-md-2 mb-3">
                <div class="card stats-card bg-success">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h3><?php echo $completed_orders; ?></h3>
                        <p>مكتملة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإجراءات السريعة -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-user-plus feature-icon"></i>
                        <h5 class="card-title">إضافة عميل جديد</h5>
                        <a href="add_customer.php" class="btn btn-primary">إضافة عميل</a>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-plus-circle feature-icon"></i>
                        <h5 class="card-title">إضافة طلبية جديدة</h5>
                        <a href="add_order.php" class="btn btn-primary">إضافة طلبية</a>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-list feature-icon"></i>
                        <h5 class="card-title">عرض جميع الطلبيات</h5>
                        <a href="view_orders.php" class="btn btn-primary">عرض الطلبيات</a>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-users-cog feature-icon"></i>
                        <h5 class="card-title">إدارة العملاء</h5>
                        <a href="manage_customers.php" class="btn btn-primary">إدارة العملاء</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- لوحة تحكم العملاء والطلبيات -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tachometer-alt"></i> لوحة تحكم العملاء والطلبيات</h5>
            </div>
            <div class="card-body">
                <?php
                // جلب العملاء الذين لديهم طلبيات معلقة أو قيد التنفيذ فقط
                $db->query("SELECT c.*,
                           COUNT(o.id) as orders_count,
                           SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_count,
                           SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_count,
                           SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_count,
                           SUM(CASE WHEN o.status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_count
                           FROM customers c
                           INNER JOIN orders o ON c.id = o.customer_id
                           WHERE o.status IN ('pending', 'processing')
                           GROUP BY c.id
                           HAVING pending_count > 0 OR processing_count > 0
                           ORDER BY pending_count DESC, processing_count DESC, c.name");
                $customers_with_active_orders = $db->resultset();
                ?>

                <?php if (count($customers_with_active_orders) > 0): ?>
                    <div class="alert alert-info mb-3">
                        <i class="fas fa-info-circle"></i>
                        <strong>يتم عرض العملاء الذين لديهم طلبيات معلقة أو قيد التنفيذ فقط</strong>
                    </div>
                    <div class="accordion" id="customersAccordion">
                        <?php foreach ($customers_with_active_orders as $index => $customer): ?>
                            <div class="accordion-item mb-2">
                                <h2 class="accordion-header" id="heading<?php echo $customer['id']; ?>">
                                    <button class="accordion-button collapsed customer-button" type="button"
                                            data-bs-toggle="collapse"
                                            data-bs-target="#collapse<?php echo $customer['id']; ?>"
                                            aria-expanded="false"
                                            aria-controls="collapse<?php echo $customer['id']; ?>"
                                            onclick="loadCustomerOrders(<?php echo $customer['id']; ?>)">
                                        <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                            <div>
                                                <i class="fas fa-user me-2"></i>
                                                <strong><?php echo htmlspecialchars($customer['name']); ?></strong>
                                                <?php if (!empty($customer['phone'])): ?>
                                                    <small class="text-muted ms-2">
                                                        <i class="fas fa-phone"></i> <?php echo htmlspecialchars($customer['phone']); ?>
                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                            <div class="customer-stats">
                                                <?php
                                                $active_orders = $customer['pending_count'] + $customer['processing_count'];
                                                ?>
                                                <span class="badge bg-primary me-1">
                                                    <i class="fas fa-exclamation-circle"></i> <?php echo $active_orders; ?> طلبية نشطة
                                                </span>
                                                <?php if ($customer['pending_count'] > 0): ?>
                                                    <span class="badge bg-warning text-dark me-1">
                                                        <i class="fas fa-clock"></i> معلقة: <?php echo $customer['pending_count']; ?>
                                                    </span>
                                                <?php endif; ?>
                                                <?php if ($customer['processing_count'] > 0): ?>
                                                    <span class="badge bg-info me-1">
                                                        <i class="fas fa-cogs"></i> قيد التنفيذ: <?php echo $customer['processing_count']; ?>
                                                    </span>
                                                <?php endif; ?>
                                                <span class="badge bg-secondary me-1">
                                                    <i class="fas fa-list"></i> إجمالي: <?php echo $customer['orders_count']; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </button>
                                </h2>
                                <div id="collapse<?php echo $customer['id']; ?>"
                                     class="accordion-collapse collapse"
                                     aria-labelledby="heading<?php echo $customer['id']; ?>"
                                     data-bs-parent="#customersAccordion">
                                    <div class="accordion-body">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6><i class="fas fa-list"></i> طلبيات العميل</h6>
                                            <a href="add_order.php?customer_id=<?php echo $customer['id']; ?>"
                                               class="btn btn-sm btn-success">
                                                <i class="fas fa-plus"></i> إضافة طلبية جديدة
                                            </a>
                                        </div>

                                        <div id="orders-container-<?php echo $customer['id']; ?>">
                                            <div class="text-center py-3">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">جاري التحميل...</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5 class="text-success">ممتاز! لا توجد طلبيات معلقة أو قيد التنفيذ</h5>
                        <p class="text-muted">جميع الطلبيات مكتملة أو لا توجد طلبيات نشطة حالياً</p>
                        <div class="mt-3">
                            <a href="add_order.php" class="btn btn-primary me-2">
                                <i class="fas fa-plus-circle"></i> إضافة طلبية جديدة
                            </a>
                            <a href="view_orders.php" class="btn btn-outline-secondary">
                                <i class="fas fa-list"></i> عرض جميع الطلبيات
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // متغير لتتبع العملاء المحملين
        const loadedCustomers = new Set();

        // تحميل طلبيات العميل
        function loadCustomerOrders(customerId) {
            // تجنب التحميل المتكرر
            if (loadedCustomers.has(customerId)) {
                return;
            }

            const container = document.getElementById(`orders-container-${customerId}`);

            // إظهار مؤشر التحميل
            container.innerHTML = `
                <div class="text-center py-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                </div>
            `;

            // محاكاة تحميل البيانات (يمكن استبدالها بـ AJAX)
            setTimeout(() => {
                fetch(`get_customer_orders.php?customer_id=${customerId}`)
                    .then(response => response.text())
                    .then(data => {
                        container.innerHTML = data;
                        loadedCustomers.add(customerId);
                    })
                    .catch(error => {
                        container.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle"></i> حدث خطأ في تحميل الطلبيات
                            </div>
                        `;
                    });
            }, 500);
        }

        // تحديث حالة الطلبية
        function updateOrderStatus(orderId, status) {
            const formData = new FormData();
            formData.append('order_id', orderId);
            formData.append('status', status);
            formData.append('update_status', '1');

            fetch('update_order_status.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إعادة تحميل الصفحة لتحديث الإحصائيات
                    location.reload();
                } else {
                    alert('حدث خطأ في تحديث الحالة');
                }
            })
            .catch(error => {
                alert('حدث خطأ في الاتصال');
            });
        }

        // تأثيرات بصرية عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير تدريجي للبطاقات
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
